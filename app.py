import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import requests
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
import json
import io
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select, WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
import time

# Page configuration
st.set_page_config(
    page_title="Maharashtra FPS Data Analyzer",
    page_icon="🏪",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .warning-card {
        background-color: #fff3cd;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #ffc107;
    }
    .success-card {
        background-color: #d4edda;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #28a745;
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'scraped_data' not in st.session_state:
    st.session_state.scraped_data = None
if 'uploaded_data' not in st.session_state:
    st.session_state.uploaded_data = None
if 'stock_data' not in st.session_state:
    st.session_state.stock_data = None

def main():
    st.markdown('<h1 class="main-header">🏪 Maharashtra FPS Data Analyzer</h1>', unsafe_allow_html=True)
    st.markdown("### Analyze Fair Price Shop transactions, stock, and compare monthly data")
    
    # Sidebar navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a section:",
        ["🏠 Dashboard", "🔍 Data Scraping", "📊 Transaction Analysis", "📦 Stock Management", "📈 Visualizations"]
    )
    
    if page == "🏠 Dashboard":
        show_dashboard()
    elif page == "🔍 Data Scraping":
        show_scraping_section()
    elif page == "📊 Transaction Analysis":
        show_transaction_analysis()
    elif page == "📦 Stock Management":
        show_stock_management()
    elif page == "📈 Visualizations":
        show_visualizations()

def show_dashboard():
    st.header("📊 Dashboard Overview")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown("""
        <div class="metric-card">
            <h4>Data Sources</h4>
            <p>• Web Scraping: Maharashtra AePDS</p>
            <p>• Upload: Previous month data</p>
            <p>• Manual: Current stock input</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="metric-card">
            <h4>Analysis Features</h4>
            <p>• Transaction comparison</p>
            <p>• Missing transaction detection</p>
            <p>• Stock level monitoring</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="metric-card">
            <h4>Visualizations</h4>
            <p>• Monthly trends</p>
            <p>• Stock vs demand charts</p>
            <p>• Performance metrics</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        st.markdown("""
        <div class="metric-card">
            <h4>Reports</h4>
            <p>• Pending transactions</p>
            <p>• Stock shortage alerts</p>
            <p>• Performance summary</p>
        </div>
        """, unsafe_allow_html=True)
    
    # Quick stats
    if st.session_state.scraped_data is not None or st.session_state.uploaded_data is not None:
        st.subheader("📈 Quick Statistics")
        
        col1, col2, col3 = st.columns(3)
        
        if st.session_state.scraped_data is not None:
            with col1:
                st.metric("Current Month Transactions", len(st.session_state.scraped_data))
        
        if st.session_state.uploaded_data is not None:
            with col2:
                st.metric("Previous Month Transactions", len(st.session_state.uploaded_data))
        
        if st.session_state.scraped_data is not None and st.session_state.uploaded_data is not None:
            with col3:
                current_count = len(st.session_state.scraped_data)
                previous_count = len(st.session_state.uploaded_data)
                change = current_count - previous_count
                st.metric("Change from Last Month", f"{change:+d}", f"{change:+d} transactions")

def show_scraping_section():
    st.header("🔍 Data Scraping from Maharashtra AePDS")
    
    st.info("This section allows you to scrape data from the Maharashtra AePDS website for FPS transactions and stock details.")
    
    # Scraping parameters
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📅 Select Parameters")
        
        # Month and Year selection
        months = ["January", "February", "March", "April", "May", "June", 
                 "July", "August", "September", "October", "November", "December"]
        selected_month = st.selectbox("Select Month:", months, index=datetime.now().month - 1)
        
        years = list(range(2019, 2026))
        selected_year = st.selectbox("Select Year:", years, index=years.index(datetime.now().year))
        
        # District selection
        districts = [
            "Ahmadnagar", "Akola", "Amravati", "A Region Parel", "Beed", "Bhandara",
            "Buldana", "Chandrapur", "Chhatrapati Sambhajinagar", "Dharashiv", "Dhule",
            "D Region Andheri", "E Region Wadala", "F Region Thane", "Gadchiroli",
            "Gondia", "G Region Kandivali", "Hingoli", "Jalgaon", "Jalna", "Kolhapur",
            "Latur", "Nagpur DSO", "Nagpur Fdo", "Nanded", "Nandurbar", "Nashik",
            "Palghar", "Parbhani", "Pune Dso", "Pune FDO", "Raigad", "Ratnagiri",
            "Sangli", "Satara", "Sindhudurg", "Solapur DSO", "Solapur FDO", "Thane",
            "Wardha", "Washim", "Yavatmal"
        ]
        selected_district = st.selectbox("Select District:", districts)
        
        fps_id = st.text_input("FPS ID (if specific):", placeholder="Leave empty for all FPS in district")
    
    with col2:
        st.subheader("🎯 Data Types to Scrape")
        
        scrape_sales = st.checkbox("Sales Register Data", value=True)
        scrape_stock = st.checkbox("Stock Details", value=True)
        scrape_transactions = st.checkbox("Transaction Details", value=True)
        
        st.subheader("⚙️ Scraping Options")
        scraping_method = st.radio(
            "Choose scraping method:",
            ["🤖 Selenium (Real website scraping)", "📊 Sample Data (For testing/demo)"],
            index=1  # Default to sample data for reliability
        )

        use_selenium = scraping_method.startswith("🤖")
        delay_between_requests = st.slider("Delay between requests (seconds):", 1, 10, 3)

        if use_selenium:
            st.warning("⚠️ Selenium scraping requires Chrome browser and may take longer. Ensure you have a stable internet connection.")
        else:
            st.info("ℹ️ Sample data mode will generate realistic test data for demonstration purposes.")
    
    # Scraping buttons
    col1, col2 = st.columns(2)

    with col1:
        if st.button("🚀 Start Data Collection", type="primary"):
            spinner_text = "Generating sample data..." if not use_selenium else "Scraping data from Maharashtra AePDS website..."

            with st.spinner(spinner_text):
                try:
                    scraped_data = scrape_fps_data(
                        selected_month, selected_year, selected_district, fps_id,
                        scrape_sales, scrape_stock, scrape_transactions,
                        use_selenium, delay_between_requests
                    )

                    if scraped_data is not None and len(scraped_data) > 0:
                        st.session_state.scraped_data = scraped_data
                        success_msg = f"✅ Successfully collected {len(scraped_data)} records!"
                        if not use_selenium:
                            success_msg += " (Sample data generated for demonstration)"
                        st.success(success_msg)

                        # Display preview
                        st.subheader("📋 Data Preview")
                        st.dataframe(scraped_data.head(10))

                        # Download option
                        csv = scraped_data.to_csv(index=False)
                        st.download_button(
                            label="📥 Download Data as CSV",
                            data=csv,
                            file_name=f"fps_data_{selected_month}_{selected_year}.csv",
                            mime="text/csv"
                        )
                    else:
                        st.error("❌ No data found or collection failed. Please check your parameters.")

                except Exception as e:
                    st.error(f"❌ Error during data collection: {str(e)}")
                    st.info("💡 Try using 'Sample Data' mode for testing the application features.")

    with col2:
        if st.button("📊 Load Demo Data", help="Load pre-generated sample data for testing"):
            try:
                # Load sample data files
                import pandas as pd
                current_data = pd.read_csv('sample_current_month_sales.csv')
                st.session_state.scraped_data = current_data
                st.success(f"✅ Loaded {len(current_data)} demo records!")

                # Display preview
                st.subheader("📋 Demo Data Preview")
                st.dataframe(current_data.head(10))

            except Exception as e:
                st.error(f"❌ Error loading demo data: {str(e)}")
                st.info("💡 Demo data files may not be available. Try generating sample data first.")
    
    # Upload previous month data
    st.subheader("📤 Upload Previous Month Data")
    uploaded_file = st.file_uploader(
        "Upload last month's transaction data (CSV/Excel):",
        type=['csv', 'xlsx', 'xls']
    )
    
    if uploaded_file is not None:
        try:
            if uploaded_file.name.endswith('.csv'):
                uploaded_data = pd.read_csv(uploaded_file)
            else:
                uploaded_data = pd.read_excel(uploaded_file)
            
            st.session_state.uploaded_data = uploaded_data
            st.success(f"✅ Successfully uploaded {len(uploaded_data)} records!")
            
            st.subheader("📋 Uploaded Data Preview")
            st.dataframe(uploaded_data.head(10))
            
        except Exception as e:
            st.error(f"❌ Error reading uploaded file: {str(e)}")

def scrape_fps_data(month, year, district, fps_id, scrape_sales, scrape_stock, scrape_transactions, use_selenium, delay):
    """
    Scrape FPS data from Maharashtra AePDS website
    """
    try:
        if use_selenium:
            return scrape_with_selenium(month, year, district, fps_id, scrape_sales, scrape_stock, scrape_transactions, delay)
        else:
            return scrape_with_requests(month, year, district, fps_id, scrape_sales, scrape_stock, scrape_transactions, delay)
    except Exception as e:
        st.error(f"Scraping error: {str(e)}")
        return None

def scrape_with_selenium(month, year, district, fps_id, scrape_sales, scrape_stock, scrape_transactions, delay):
    """
    Scrape data using Selenium for dynamic content
    """
    # Setup Chrome options
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")

    try:
        # Initialize driver with proper service
        from selenium.webdriver.chrome.service import Service
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        all_data = []
        
        if scrape_sales:
            # Scrape sales register
            sales_data = scrape_sales_register(driver, month, year, district, fps_id, delay)
            if sales_data:
                all_data.extend(sales_data)
        
        if scrape_stock:
            # Scrape stock details
            stock_data = scrape_stock_details(driver, month, year, district, fps_id, delay)
            if stock_data:
                all_data.extend(stock_data)
        
        driver.quit()

        if all_data:
            return pd.DataFrame(all_data)
        else:
            return None

    except Exception as e:
        st.error(f"Selenium scraping error: {str(e)}")
        st.info("💡 Common solutions:")
        st.info("• Ensure Google Chrome is installed")
        st.info("• Check your internet connection")
        st.info("• Try using 'Sample Data' mode instead")
        st.info("• The government website might be temporarily unavailable")

        # Try to quit driver if it was created
        try:
            if 'driver' in locals():
                driver.quit()
        except:
            pass

        return None

def scrape_sales_register(driver, month, year, district, fps_id, delay):
    """
    Scrape FPS Sales Register data
    """
    try:
        # Navigate to sales register page
        driver.get("https://mahaepos.gov.in/FPS_Trans_Abstract.jsp")
        time.sleep(delay)
        
        # Select month
        month_select = Select(driver.find_element(By.NAME, "month"))
        month_select.select_by_visible_text(month)
        
        # Select year
        year_select = Select(driver.find_element(By.NAME, "year"))
        year_select.select_by_visible_text(str(year))
        
        # Select district
        district_select = Select(driver.find_element(By.NAME, "district"))
        district_select.select_by_visible_text(district)
        
        # If FPS ID is provided, select it
        if fps_id:
            fps_select = Select(driver.find_element(By.NAME, "fps"))
            fps_select.select_by_value(fps_id)
        
        # Submit form
        submit_button = driver.find_element(By.XPATH, "//input[@type='submit']")
        submit_button.click()
        
        time.sleep(delay * 2)  # Wait for results to load
        
        # Parse results
        sales_data = parse_sales_data(driver.page_source)
        return sales_data
        
    except Exception as e:
        st.error(f"Error scraping sales register: {str(e)}")
        return None

def scrape_stock_details(driver, month, year, district, fps_id, delay):
    """
    Scrape FPS Stock Details data
    """
    try:
        # Navigate to stock details page
        driver.get("https://mahaepos.gov.in/Stock_Register_Int.jsp")
        time.sleep(delay)
        
        # Select month
        month_select = Select(driver.find_element(By.NAME, "month"))
        month_select.select_by_visible_text(month)
        
        # Select year
        year_select = Select(driver.find_element(By.NAME, "year"))
        year_select.select_by_visible_text(str(year))
        
        # Select district
        district_select = Select(driver.find_element(By.NAME, "district"))
        district_select.select_by_visible_text(district)
        
        # If FPS ID is provided, select it
        if fps_id:
            fps_select = Select(driver.find_element(By.NAME, "fps"))
            fps_select.select_by_value(fps_id)
        
        # Submit form
        submit_button = driver.find_element(By.XPATH, "//input[@type='submit']")
        submit_button.click()
        
        time.sleep(delay * 2)  # Wait for results to load
        
        # Parse results
        stock_data = parse_stock_data(driver.page_source)
        return stock_data
        
    except Exception as e:
        st.error(f"Error scraping stock details: {str(e)}")
        return None

def parse_sales_data(html_content):
    """
    Parse sales data from HTML content
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find data tables
    tables = soup.find_all('table')
    
    sales_data = []
    
    for table in tables:
        rows = table.find_all('tr')
        
        for row in rows[1:]:  # Skip header row
            cells = row.find_all(['td', 'th'])
            
            if len(cells) >= 4:  # Ensure minimum required columns
                record = {
                    'type': 'sales',
                    'fps_id': cells[0].get_text(strip=True) if len(cells) > 0 else '',
                    'beneficiary_id': cells[1].get_text(strip=True) if len(cells) > 1 else '',
                    'commodity': cells[2].get_text(strip=True) if len(cells) > 2 else '',
                    'quantity': cells[3].get_text(strip=True) if len(cells) > 3 else '',
                    'amount': cells[4].get_text(strip=True) if len(cells) > 4 else '',
                    'date': cells[5].get_text(strip=True) if len(cells) > 5 else '',
                    'scraped_at': datetime.now().isoformat()
                }
                sales_data.append(record)
    
    return sales_data

def parse_stock_data(html_content):
    """
    Parse stock data from HTML content
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Find data tables
    tables = soup.find_all('table')
    
    stock_data = []
    
    for table in tables:
        rows = table.find_all('tr')
        
        for row in rows[1:]:  # Skip header row
            cells = row.find_all(['td', 'th'])
            
            if len(cells) >= 3:  # Ensure minimum required columns
                record = {
                    'type': 'stock',
                    'fps_id': cells[0].get_text(strip=True) if len(cells) > 0 else '',
                    'commodity': cells[1].get_text(strip=True) if len(cells) > 1 else '',
                    'opening_stock': cells[2].get_text(strip=True) if len(cells) > 2 else '',
                    'received': cells[3].get_text(strip=True) if len(cells) > 3 else '',
                    'issued': cells[4].get_text(strip=True) if len(cells) > 4 else '',
                    'closing_stock': cells[5].get_text(strip=True) if len(cells) > 5 else '',
                    'scraped_at': datetime.now().isoformat()
                }
                stock_data.append(record)
    
    return stock_data

def scrape_with_requests(month, year, district, fps_id, scrape_sales, scrape_stock, scrape_transactions, delay):
    """
    Scrape data using requests for static content (fallback method)
    """
    try:
        st.info("🔄 Using fallback method - generating sample data for demonstration...")

        # Generate sample data as fallback
        from sample_data_generator import generate_sample_fps_data

        all_data = []

        if scrape_sales:
            sales_data = generate_sample_fps_data(100, 'sales')
            all_data.append(sales_data)

        if scrape_stock:
            stock_data = generate_sample_fps_data(50, 'stock')
            all_data.append(stock_data)

        if all_data:
            import pandas as pd
            combined_data = pd.concat(all_data, ignore_index=True)
            return combined_data

        return None

    except Exception as e:
        st.error(f"Fallback method error: {str(e)}")
        return None

def show_transaction_analysis():
    st.header("📊 Transaction Analysis")

    if st.session_state.scraped_data is None and st.session_state.uploaded_data is None:
        st.warning("⚠️ Please scrape current data or upload previous month data first.")
        return

    # Analysis options
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📈 Current Month Analysis")
        if st.session_state.scraped_data is not None:
            current_data = st.session_state.scraped_data

            # Basic statistics
            st.metric("Total Transactions", len(current_data))

            if 'amount' in current_data.columns:
                total_amount = pd.to_numeric(current_data['amount'], errors='coerce').sum()
                st.metric("Total Amount", f"₹{total_amount:,.2f}")

            # Transaction by commodity
            if 'commodity' in current_data.columns:
                commodity_counts = current_data['commodity'].value_counts()
                fig = px.pie(
                    values=commodity_counts.values,
                    names=commodity_counts.index,
                    title="Transactions by Commodity"
                )
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No current month data available")

    with col2:
        st.subheader("📉 Previous Month Analysis")
        if st.session_state.uploaded_data is not None:
            previous_data = st.session_state.uploaded_data

            # Basic statistics
            st.metric("Total Transactions", len(previous_data))

            if 'amount' in previous_data.columns:
                total_amount = pd.to_numeric(previous_data['amount'], errors='coerce').sum()
                st.metric("Total Amount", f"₹{total_amount:,.2f}")

            # Transaction by commodity
            if 'commodity' in previous_data.columns:
                commodity_counts = previous_data['commodity'].value_counts()
                fig = px.pie(
                    values=commodity_counts.values,
                    names=commodity_counts.index,
                    title="Previous Month Transactions by Commodity"
                )
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No previous month data available")

    # Comparison analysis
    if st.session_state.scraped_data is not None and st.session_state.uploaded_data is not None:
        st.subheader("🔍 Missing Transactions Analysis")

        current_data = st.session_state.scraped_data
        previous_data = st.session_state.uploaded_data

        # Find missing transactions
        missing_transactions = find_missing_transactions(current_data, previous_data)

        if not missing_transactions.empty:
            st.error(f"⚠️ Found {len(missing_transactions)} transactions that were done last month but not this month!")

            # Display missing transactions
            st.dataframe(missing_transactions)

            # Download missing transactions
            csv = missing_transactions.to_csv(index=False)
            st.download_button(
                label="📥 Download Missing Transactions",
                data=csv,
                file_name="missing_transactions.csv",
                mime="text/csv"
            )

            # Visualize missing transactions by commodity
            if 'commodity' in missing_transactions.columns:
                missing_by_commodity = missing_transactions['commodity'].value_counts()
                fig = px.bar(
                    x=missing_by_commodity.index,
                    y=missing_by_commodity.values,
                    title="Missing Transactions by Commodity",
                    labels={'x': 'Commodity', 'y': 'Count'}
                )
                st.plotly_chart(fig, use_container_width=True)
        else:
            st.success("✅ No missing transactions found!")

def find_missing_transactions(current_data, previous_data):
    """
    Find transactions that were done last month but not this month
    """
    try:
        # Create unique identifiers for transactions
        if 'beneficiary_id' in previous_data.columns and 'commodity' in previous_data.columns:
            previous_transactions = set(
                previous_data['beneficiary_id'].astype(str) + "_" + previous_data['commodity'].astype(str)
            )

            if 'beneficiary_id' in current_data.columns and 'commodity' in current_data.columns:
                current_transactions = set(
                    current_data['beneficiary_id'].astype(str) + "_" + current_data['commodity'].astype(str)
                )

                # Find missing transaction IDs
                missing_ids = previous_transactions - current_transactions

                # Filter previous data for missing transactions
                previous_data_copy = previous_data.copy()
                previous_data_copy['transaction_id'] = (
                    previous_data_copy['beneficiary_id'].astype(str) + "_" + previous_data_copy['commodity'].astype(str)
                )

                missing_transactions = previous_data_copy[previous_data_copy['transaction_id'].isin(missing_ids)]
                return missing_transactions.drop('transaction_id', axis=1)

        return pd.DataFrame()

    except Exception as e:
        st.error(f"Error finding missing transactions: {str(e)}")
        return pd.DataFrame()

def show_stock_management():
    st.header("📦 Stock Management")

    # Input current stock
    st.subheader("📝 Input Current Shop Stock")

    # Common commodities in Maharashtra FPS
    commodities = [
        "Rice", "Wheat", "Sugar", "Kerosene", "Jowar", "Bajra", "Ragi",
        "Chana Dal", "Rava", "Maize", "Black Gram", "Coarse Grain"
    ]

    stock_input = {}

    col1, col2 = st.columns(2)

    with col1:
        st.write("**Basic Commodities:**")
        for i, commodity in enumerate(commodities[:6]):
            stock_input[commodity] = st.number_input(
                f"{commodity} (kg):",
                min_value=0.0,
                value=0.0,
                step=0.1,
                key=f"stock_{commodity}"
            )

    with col2:
        st.write("**Additional Commodities:**")
        for i, commodity in enumerate(commodities[6:]):
            stock_input[commodity] = st.number_input(
                f"{commodity} (kg):",
                min_value=0.0,
                value=0.0,
                step=0.1,
                key=f"stock_{commodity}"
            )

    # Add custom commodity
    st.subheader("➕ Add Custom Commodity")
    custom_commodity = st.text_input("Custom Commodity Name:")
    custom_quantity = st.number_input("Quantity (kg):", min_value=0.0, value=0.0, step=0.1)

    if custom_commodity and custom_quantity > 0:
        stock_input[custom_commodity] = custom_quantity

    # Save stock data
    if st.button("💾 Save Stock Data"):
        # Convert to DataFrame
        stock_df = pd.DataFrame([
            {"commodity": commodity, "current_stock": quantity}
            for commodity, quantity in stock_input.items()
            if quantity > 0
        ])

        st.session_state.stock_data = stock_df
        st.success("✅ Stock data saved successfully!")

        # Display current stock
        st.subheader("📊 Current Stock Summary")
        st.dataframe(stock_df)

    # Stock analysis
    if st.session_state.stock_data is not None:
        st.subheader("📈 Stock Analysis")

        stock_df = st.session_state.stock_data

        # Stock level visualization
        fig = px.bar(
            stock_df,
            x='commodity',
            y='current_stock',
            title="Current Stock Levels",
            labels={'current_stock': 'Stock (kg)', 'commodity': 'Commodity'}
        )
        fig.update_xaxis(tickangle=45)
        st.plotly_chart(fig, use_container_width=True)

        # Compare with scraped stock data if available
        if st.session_state.scraped_data is not None:
            scraped_stock = st.session_state.scraped_data[
                st.session_state.scraped_data['type'] == 'stock'
            ] if 'type' in st.session_state.scraped_data.columns else pd.DataFrame()

            if not scraped_stock.empty:
                st.subheader("🔍 Stock Comparison with Government Records")

                # Merge data for comparison
                comparison_data = compare_stock_data(stock_df, scraped_stock)

                if not comparison_data.empty:
                    st.dataframe(comparison_data)

                    # Highlight discrepancies
                    discrepancies = comparison_data[
                        abs(comparison_data['difference']) > 0.1
                    ]

                    if not discrepancies.empty:
                        st.warning(f"⚠️ Found {len(discrepancies)} stock discrepancies!")
                        st.dataframe(discrepancies)

    # Stock alerts
    if st.session_state.stock_data is not None:
        st.subheader("🚨 Stock Alerts")

        stock_df = st.session_state.stock_data

        # Low stock threshold
        low_stock_threshold = st.slider("Low Stock Threshold (kg):", 1, 100, 10)

        low_stock_items = stock_df[stock_df['current_stock'] < low_stock_threshold]

        if not low_stock_items.empty:
            st.error(f"⚠️ {len(low_stock_items)} items are below the threshold!")

            for _, item in low_stock_items.iterrows():
                st.markdown(f"""
                <div class="warning-card">
                    <strong>{item['commodity']}</strong>: {item['current_stock']} kg remaining
                </div>
                """, unsafe_allow_html=True)
        else:
            st.success("✅ All items are above the low stock threshold!")

def compare_stock_data(current_stock, scraped_stock):
    """
    Compare current stock with scraped government data
    """
    try:
        # Prepare scraped stock data
        if 'commodity' in scraped_stock.columns and 'closing_stock' in scraped_stock.columns:
            scraped_stock['closing_stock'] = pd.to_numeric(scraped_stock['closing_stock'], errors='coerce')
            scraped_summary = scraped_stock.groupby('commodity')['closing_stock'].sum().reset_index()
            scraped_summary.columns = ['commodity', 'government_stock']

            # Merge with current stock
            comparison = pd.merge(current_stock, scraped_summary, on='commodity', how='outer')
            comparison = comparison.fillna(0)

            # Calculate difference
            comparison['difference'] = comparison['current_stock'] - comparison['government_stock']
            comparison['status'] = comparison['difference'].apply(
                lambda x: 'Excess' if x > 0 else 'Shortage' if x < 0 else 'Match'
            )

            return comparison

        return pd.DataFrame()

    except Exception as e:
        st.error(f"Error comparing stock data: {str(e)}")
        return pd.DataFrame()

def show_stock_management():
    st.header("📦 Stock Management")

    # Input current stock
    st.subheader("📝 Input Current Shop Stock")

    # Common commodities in Maharashtra FPS
    commodities = [
        "Rice", "Wheat", "Sugar", "Kerosene", "Jowar", "Bajra", "Ragi",
        "Chana Dal", "Rava", "Maize", "Black Gram", "Coarse Grain"
    ]

    stock_input = {}

    col1, col2 = st.columns(2)

    with col1:
        st.write("**Basic Commodities:**")
        for i, commodity in enumerate(commodities[:6]):
            stock_input[commodity] = st.number_input(
                f"{commodity} (kg):",
                min_value=0.0,
                value=0.0,
                step=0.1,
                key=f"stock_{commodity}"
            )

    with col2:
        st.write("**Additional Commodities:**")
        for i, commodity in enumerate(commodities[6:]):
            stock_input[commodity] = st.number_input(
                f"{commodity} (kg):",
                min_value=0.0,
                value=0.0,
                step=0.1,
                key=f"stock_{commodity}"
            )

    # Add custom commodity
    st.subheader("➕ Add Custom Commodity")
    custom_commodity = st.text_input("Custom Commodity Name:")
    custom_quantity = st.number_input("Quantity (kg):", min_value=0.0, value=0.0, step=0.1)

    if custom_commodity and custom_quantity > 0:
        stock_input[custom_commodity] = custom_quantity

    # Save stock data
    if st.button("💾 Save Stock Data"):
        # Convert to DataFrame
        stock_df = pd.DataFrame([
            {"commodity": commodity, "current_stock": quantity}
            for commodity, quantity in stock_input.items()
            if quantity > 0
        ])

        st.session_state.stock_data = stock_df
        st.success("✅ Stock data saved successfully!")

        # Display current stock
        st.subheader("📊 Current Stock Summary")
        st.dataframe(stock_df)

def compare_stock_data(current_stock, scraped_stock):
    """
    Compare current stock with scraped government data
    """
    try:
        # Prepare scraped stock data
        if 'commodity' in scraped_stock.columns and 'closing_stock' in scraped_stock.columns:
            scraped_stock['closing_stock'] = pd.to_numeric(scraped_stock['closing_stock'], errors='coerce')
            scraped_summary = scraped_stock.groupby('commodity')['closing_stock'].sum().reset_index()
            scraped_summary.columns = ['commodity', 'government_stock']

            # Merge with current stock
            comparison = pd.merge(current_stock, scraped_summary, on='commodity', how='outer')
            comparison = comparison.fillna(0)

            # Calculate difference
            comparison['difference'] = comparison['current_stock'] - comparison['government_stock']
            comparison['status'] = comparison['difference'].apply(
                lambda x: 'Excess' if x > 0 else 'Shortage' if x < 0 else 'Match'
            )

            return comparison

        return pd.DataFrame()

    except Exception as e:
        st.error(f"Error comparing stock data: {str(e)}")
        return pd.DataFrame()

def show_visualizations():
    st.header("📈 Data Visualizations")

    if st.session_state.scraped_data is None and st.session_state.uploaded_data is None and st.session_state.stock_data is None:
        st.warning("⚠️ Please load some data first to view visualizations.")
        return

    # Visualization options
    viz_type = st.selectbox(
        "Select Visualization Type:",
        ["📊 Transaction Trends", "📦 Stock Analysis", "🔍 Comparison Charts", "📈 Performance Metrics"]
    )

    if viz_type == "📊 Transaction Trends":
        show_transaction_trends()
    elif viz_type == "📦 Stock Analysis":
        show_stock_visualizations()
    elif viz_type == "🔍 Comparison Charts":
        show_comparison_charts()
    elif viz_type == "📈 Performance Metrics":
        show_performance_metrics()

def show_transaction_trends():
    st.subheader("📊 Transaction Trends Analysis")

    if st.session_state.scraped_data is not None:
        data = st.session_state.scraped_data

        # Transaction volume by commodity
        if 'commodity' in data.columns:
            commodity_counts = data['commodity'].value_counts()

            col1, col2 = st.columns(2)

            with col1:
                # Bar chart
                fig_bar = px.bar(
                    x=commodity_counts.index,
                    y=commodity_counts.values,
                    title="Transaction Volume by Commodity",
                    labels={'x': 'Commodity', 'y': 'Number of Transactions'}
                )
                fig_bar.update_xaxis(tickangle=45)
                st.plotly_chart(fig_bar, use_container_width=True)

            with col2:
                # Pie chart
                fig_pie = px.pie(
                    values=commodity_counts.values,
                    names=commodity_counts.index,
                    title="Transaction Distribution"
                )
                st.plotly_chart(fig_pie, use_container_width=True)

        # Amount analysis if available
        if 'amount' in data.columns:
            data['amount_numeric'] = pd.to_numeric(data['amount'], errors='coerce')
            amount_by_commodity = data.groupby('commodity')['amount_numeric'].sum().sort_values(ascending=False)

            fig_amount = px.bar(
                x=amount_by_commodity.index,
                y=amount_by_commodity.values,
                title="Total Amount by Commodity",
                labels={'x': 'Commodity', 'y': 'Total Amount (₹)'}
            )
            fig_amount.update_xaxis(tickangle=45)
            st.plotly_chart(fig_amount, use_container_width=True)

        # Time series if date available
        if 'date' in data.columns:
            try:
                data['date_parsed'] = pd.to_datetime(data['date'], errors='coerce')
                daily_transactions = data.groupby(data['date_parsed'].dt.date).size()

                fig_time = px.line(
                    x=daily_transactions.index,
                    y=daily_transactions.values,
                    title="Daily Transaction Trends",
                    labels={'x': 'Date', 'y': 'Number of Transactions'}
                )
                st.plotly_chart(fig_time, use_container_width=True)
            except:
                st.info("Date parsing not available for time series analysis")

def show_stock_visualizations():
    st.subheader("📦 Stock Analysis Visualizations")

    if st.session_state.stock_data is not None:
        stock_df = st.session_state.stock_data

        col1, col2 = st.columns(2)

        with col1:
            # Current stock levels
            fig_stock = px.bar(
                stock_df,
                x='commodity',
                y='current_stock',
                title="Current Stock Levels",
                labels={'current_stock': 'Stock (kg)', 'commodity': 'Commodity'},
                color='current_stock',
                color_continuous_scale='Viridis'
            )
            fig_stock.update_xaxis(tickangle=45)
            st.plotly_chart(fig_stock, use_container_width=True)

        with col2:
            # Stock distribution pie chart
            fig_stock_pie = px.pie(
                stock_df,
                values='current_stock',
                names='commodity',
                title="Stock Distribution by Commodity"
            )
            st.plotly_chart(fig_stock_pie, use_container_width=True)

        # Stock level indicators
        st.subheader("🚦 Stock Level Indicators")

        # Define thresholds
        high_threshold = st.slider("High Stock Threshold (kg):", 50, 500, 100)
        medium_threshold = st.slider("Medium Stock Threshold (kg):", 20, 100, 50)
        low_threshold = st.slider("Low Stock Threshold (kg):", 1, 50, 10)

        # Categorize stock levels
        def categorize_stock(quantity):
            if quantity >= high_threshold:
                return 'High'
            elif quantity >= medium_threshold:
                return 'Medium'
            elif quantity >= low_threshold:
                return 'Low'
            else:
                return 'Critical'

        stock_df['stock_level'] = stock_df['current_stock'].apply(categorize_stock)

        # Stock level distribution
        level_counts = stock_df['stock_level'].value_counts()

        colors = {'High': 'green', 'Medium': 'yellow', 'Low': 'orange', 'Critical': 'red'}
        fig_levels = px.bar(
            x=level_counts.index,
            y=level_counts.values,
            title="Stock Level Distribution",
            labels={'x': 'Stock Level', 'y': 'Number of Items'},
            color=level_counts.index,
            color_discrete_map=colors
        )
        st.plotly_chart(fig_levels, use_container_width=True)

def show_comparison_charts():
    st.subheader("🔍 Comparison Analysis")

    if st.session_state.scraped_data is not None and st.session_state.uploaded_data is not None:
        current_data = st.session_state.scraped_data
        previous_data = st.session_state.uploaded_data

        # Compare transaction volumes
        if 'commodity' in current_data.columns and 'commodity' in previous_data.columns:
            current_counts = current_data['commodity'].value_counts()
            previous_counts = previous_data['commodity'].value_counts()

            # Combine data for comparison
            comparison_df = pd.DataFrame({
                'Current Month': current_counts,
                'Previous Month': previous_counts
            }).fillna(0)

            # Side-by-side bar chart
            fig_comparison = go.Figure()

            fig_comparison.add_trace(go.Bar(
                name='Current Month',
                x=comparison_df.index,
                y=comparison_df['Current Month'],
                marker_color='lightblue'
            ))

            fig_comparison.add_trace(go.Bar(
                name='Previous Month',
                x=comparison_df.index,
                y=comparison_df['Previous Month'],
                marker_color='lightcoral'
            ))

            fig_comparison.update_layout(
                title='Transaction Volume Comparison',
                xaxis_title='Commodity',
                yaxis_title='Number of Transactions',
                barmode='group'
            )

            st.plotly_chart(fig_comparison, use_container_width=True)

            # Calculate percentage change
            comparison_df['Change %'] = (
                (comparison_df['Current Month'] - comparison_df['Previous Month']) /
                comparison_df['Previous Month'] * 100
            ).fillna(0)

            # Show percentage change
            fig_change = px.bar(
                x=comparison_df.index,
                y=comparison_df['Change %'],
                title="Percentage Change in Transactions",
                labels={'x': 'Commodity', 'y': 'Change (%)'},
                color=comparison_df['Change %'],
                color_continuous_scale='RdYlGn'
            )
            fig_change.update_xaxis(tickangle=45)
            st.plotly_chart(fig_change, use_container_width=True)

def show_performance_metrics():
    st.subheader("📈 Performance Metrics Dashboard")

    # Create metrics based on available data
    col1, col2, col3, col4 = st.columns(4)

    if st.session_state.scraped_data is not None:
        current_data = st.session_state.scraped_data

        with col1:
            total_transactions = len(current_data)
            st.metric("Total Transactions", total_transactions)

        with col2:
            if 'commodity' in current_data.columns:
                unique_commodities = current_data['commodity'].nunique()
                st.metric("Commodities Sold", unique_commodities)

        with col3:
            if 'amount' in current_data.columns:
                total_amount = pd.to_numeric(current_data['amount'], errors='coerce').sum()
                st.metric("Total Revenue", f"₹{total_amount:,.2f}")

        with col4:
            if 'beneficiary_id' in current_data.columns:
                unique_beneficiaries = current_data['beneficiary_id'].nunique()
                st.metric("Unique Beneficiaries", unique_beneficiaries)

    # Performance indicators
    if st.session_state.scraped_data is not None and st.session_state.uploaded_data is not None:
        st.subheader("📊 Month-over-Month Performance")

        current_data = st.session_state.scraped_data
        previous_data = st.session_state.uploaded_data

        # Calculate key metrics
        current_transactions = len(current_data)
        previous_transactions = len(previous_data)
        transaction_change = current_transactions - previous_transactions
        transaction_change_pct = (transaction_change / previous_transactions * 100) if previous_transactions > 0 else 0

        col1, col2 = st.columns(2)

        with col1:
            st.metric(
                "Transaction Change",
                f"{transaction_change:+d}",
                f"{transaction_change_pct:+.1f}%"
            )

        with col2:
            if 'amount' in current_data.columns and 'amount' in previous_data.columns:
                current_amount = pd.to_numeric(current_data['amount'], errors='coerce').sum()
                previous_amount = pd.to_numeric(previous_data['amount'], errors='coerce').sum()
                amount_change = current_amount - previous_amount
                amount_change_pct = (amount_change / previous_amount * 100) if previous_amount > 0 else 0

                st.metric(
                    "Revenue Change",
                    f"₹{amount_change:+,.2f}",
                    f"{amount_change_pct:+.1f}%"
                )

if __name__ == "__main__":
    main()
