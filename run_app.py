#!/usr/bin/env python3
"""
Launcher script for Maharashtra FPS Data Analyzer
"""

import subprocess
import sys
import os

def check_dependencies():
    """Check if required packages are installed"""
    try:
        import streamlit
        import pandas
        import plotly
        import selenium
        import requests
        import beautifulsoup4
        print("✅ All required packages are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing package: {e}")
        print("Please install requirements: pip install -r requirements.txt")
        return False

def generate_sample_data():
    """Generate sample data for testing"""
    try:
        from sample_data_generator import save_sample_data
        print("🔄 Generating sample data...")
        save_sample_data()
        print("✅ Sample data generated successfully")
    except Exception as e:
        print(f"⚠️ Could not generate sample data: {e}")

def main():
    print("🏪 Maharashtra FPS Data Analyzer")
    print("=" * 40)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Ask if user wants to generate sample data
    response = input("\n📊 Generate sample data for testing? (y/n): ").lower().strip()
    if response in ['y', 'yes']:
        generate_sample_data()
    
    print("\n🚀 Starting Streamlit application...")
    print("📱 The app will open in your default browser")
    print("🛑 Press Ctrl+C to stop the application")
    print("-" * 40)
    
    # Run Streamlit app
    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "app.py"], check=True)
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running Streamlit: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
