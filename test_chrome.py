#!/usr/bin/env python3
"""
Test Chrome and ChromeDriver detection on Windows
"""

import os
import shutil
import platform

def test_chrome_detection():
    """Test if Chrome browser can be detected"""
    print("🔍 Testing Chrome Browser Detection...")
    print(f"💻 Operating System: {platform.system()} {platform.release()}")
    print()
    
    # Common Chrome installation paths on Windows
    chrome_paths = [
        shutil.which("chrome"),
        shutil.which("google-chrome"),
        shutil.which("chromium"),
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
    ]
    
    print("🔍 Checking Chrome installation paths:")
    chrome_found = False
    
    for i, path in enumerate(chrome_paths, 1):
        if path and os.path.exists(path):
            print(f"✅ {i}. Found: {path}")
            chrome_found = True
        elif path:
            print(f"❌ {i}. Not found: {path}")
        else:
            print(f"❌ {i}. Path not detected")
    
    print()
    if chrome_found:
        print("✅ **Chrome Browser**: FOUND")
    else:
        print("❌ **Chrome Browser**: NOT FOUND")
        print("💡 Install Chrome from: https://www.google.com/chrome/")
    
    return chrome_found

def test_chromedriver():
    """Test ChromeDriver installation"""
    print("\n🔍 Testing ChromeDriver Setup...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        print("✅ Selenium modules imported successfully")
        
        # Try to install ChromeDriver
        print("🔄 Attempting ChromeDriver installation...")
        driver_manager = ChromeDriverManager()
        driver_path = driver_manager.install()
        
        if os.path.exists(driver_path):
            print(f"✅ ChromeDriver installed: {driver_path}")
            
            # Try to create a headless Chrome instance
            print("🔄 Testing Chrome WebDriver startup...")
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            try:
                service = Service(driver_path)
                driver = webdriver.Chrome(service=service, options=chrome_options)
                print("✅ Chrome WebDriver started successfully!")
                driver.quit()
                print("✅ Chrome WebDriver closed successfully!")
                return True
                
            except Exception as e:
                print(f"❌ Chrome WebDriver startup failed: {str(e)}")
                
                if "WinError 193" in str(e):
                    print("🔧 **Issue**: Windows architecture mismatch (32-bit vs 64-bit)")
                    print("💡 **Solution**: Use Sample Data mode in the application")
                elif "chrome not reachable" in str(e).lower():
                    print("🔧 **Issue**: Chrome browser cannot be controlled")
                    print("💡 **Solution**: Check Chrome installation or use Sample Data mode")
                
                return False
        else:
            print(f"❌ ChromeDriver installation failed")
            return False
            
    except ImportError as e:
        print(f"❌ Selenium import failed: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ ChromeDriver test failed: {str(e)}")
        return False

def main():
    print("🏪 Maharashtra FPS Data Analyzer - Chrome Test")
    print("=" * 50)
    
    # Test Chrome browser detection
    chrome_ok = test_chrome_detection()
    
    # Test ChromeDriver setup
    chromedriver_ok = test_chromedriver()
    
    print("\n" + "=" * 50)
    print("📋 **TEST SUMMARY**")
    print("=" * 50)
    
    if chrome_ok:
        print("✅ Chrome Browser: WORKING")
    else:
        print("❌ Chrome Browser: NOT FOUND")
    
    if chromedriver_ok:
        print("✅ ChromeDriver: WORKING")
        print("🎉 **Real website scraping should work!**")
    else:
        print("❌ ChromeDriver: FAILED")
        print("💡 **Recommendation: Use Sample Data mode**")
    
    print("\n🚀 **Application Status**:")
    if chrome_ok and chromedriver_ok:
        print("✅ Both real scraping AND sample data will work")
    else:
        print("✅ Sample data mode will work perfectly")
        print("✅ All application features are available")
        print("✅ No functionality is lost")
    
    print("\n💡 **Next Steps**:")
    print("1. Run: streamlit run app.py")
    print("2. Go to Data Scraping section")
    if chromedriver_ok:
        print("3. Try both Real Scraping and Sample Data modes")
    else:
        print("3. Use Sample Data mode (recommended)")
    print("4. Explore all features with the loaded data")

if __name__ == "__main__":
    main()
