#!/usr/bin/env python3
"""
Fix ChromeDriver architecture mismatch for Windows
"""

import os
import platform
import requests
import zipfile
import shutil
from pathlib import Path

def get_chrome_version():
    """Get installed Chrome version"""
    try:
        import subprocess
        import re
        
        # Try to get Chrome version from registry
        cmd = r'reg query "HKEY_CURRENT_USER\Software\Google\Chrome\BLBeacon" /v version'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            version_line = result.stdout.strip().split('\n')[-1]
            version = re.search(r'(\d+\.\d+\.\d+\.\d+)', version_line)
            if version:
                return version.group(1)
        
        # Alternative method - check Chrome executable
        chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
        if os.path.exists(chrome_path):
            result = subprocess.run([chrome_path, '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                version = re.search(r'(\d+\.\d+\.\d+\.\d+)', result.stdout)
                if version:
                    return version.group(1)
        
        return None
    except Exception as e:
        print(f"Error getting Chrome version: {e}")
        return None

def download_correct_chromedriver():
    """Download the correct ChromeDriver for Windows"""
    try:
        print("🔍 Detecting system architecture...")
        
        # Get system architecture
        arch = platform.machine().lower()
        is_64bit = platform.architecture()[0] == '64bit'
        
        print(f"💻 System: {platform.system()} {platform.release()}")
        print(f"🏗️ Architecture: {arch} ({'64-bit' if is_64bit else '32-bit'})")
        
        # Get Chrome version
        chrome_version = get_chrome_version()
        if chrome_version:
            major_version = chrome_version.split('.')[0]
            print(f"🌐 Chrome Version: {chrome_version} (Major: {major_version})")
        else:
            print("⚠️ Could not detect Chrome version, using latest")
            major_version = "latest"
        
        # Determine correct ChromeDriver URL
        if major_version == "latest":
            # Get latest version
            response = requests.get("https://chromedriver.storage.googleapis.com/LATEST_RELEASE")
            if response.status_code == 200:
                driver_version = response.text.strip()
            else:
                print("❌ Could not get latest ChromeDriver version")
                return None
        else:
            # Try to get matching version
            try:
                response = requests.get(f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}")
                if response.status_code == 200:
                    driver_version = response.text.strip()
                else:
                    # Fallback to latest
                    response = requests.get("https://chromedriver.storage.googleapis.com/LATEST_RELEASE")
                    driver_version = response.text.strip()
            except:
                response = requests.get("https://chromedriver.storage.googleapis.com/LATEST_RELEASE")
                driver_version = response.text.strip()
        
        print(f"📥 ChromeDriver Version: {driver_version}")
        
        # Determine correct platform
        if is_64bit:
            platform_name = "win64"
        else:
            platform_name = "win32"
        
        # Download URL
        download_url = f"https://chromedriver.storage.googleapis.com/{driver_version}/chromedriver_{platform_name}.zip"
        print(f"🔗 Download URL: {download_url}")
        
        # Create download directory
        download_dir = Path.home() / ".chromedriver_fixed"
        download_dir.mkdir(exist_ok=True)
        
        zip_path = download_dir / "chromedriver.zip"
        driver_path = download_dir / "chromedriver.exe"
        
        # Download ChromeDriver
        print("📥 Downloading ChromeDriver...")
        response = requests.get(download_url)
        
        if response.status_code == 200:
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            print("✅ Download completed")
            
            # Extract ChromeDriver
            print("📦 Extracting ChromeDriver...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(download_dir)
            
            # Remove zip file
            zip_path.unlink()
            
            if driver_path.exists():
                print(f"✅ ChromeDriver extracted to: {driver_path}")
                return str(driver_path)
            else:
                print("❌ ChromeDriver extraction failed")
                return None
        else:
            print(f"❌ Download failed: HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error downloading ChromeDriver: {e}")
        return None

def test_chromedriver(driver_path):
    """Test if ChromeDriver works"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        print("🧪 Testing ChromeDriver...")
        
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Test basic functionality
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ ChromeDriver test successful! (Page title: {title})")
        return True
        
    except Exception as e:
        print(f"❌ ChromeDriver test failed: {e}")
        return False

def main():
    print("🔧 ChromeDriver Architecture Fix for Windows")
    print("=" * 50)
    
    # Download correct ChromeDriver
    driver_path = download_correct_chromedriver()
    
    if driver_path:
        # Test ChromeDriver
        if test_chromedriver(driver_path):
            print("\n🎉 SUCCESS!")
            print("=" * 50)
            print(f"✅ Fixed ChromeDriver location: {driver_path}")
            print("\n📋 Next Steps:")
            print("1. Copy this path for manual use, OR")
            print("2. Update the application to use this path, OR")
            print("3. Replace the webdriver-manager ChromeDriver with this one")
            
            # Provide instructions for manual replacement
            print(f"\n🔧 Manual Fix Instructions:")
            print("1. Find your webdriver-manager cache directory")
            print("2. Replace the chromedriver.exe with the fixed one")
            print(f"3. Fixed ChromeDriver is at: {driver_path}")
            
        else:
            print("\n❌ ChromeDriver still not working")
            print("💡 This might be a deeper compatibility issue")
            print("💡 Recommendation: Use the application's Sample Data mode")
    else:
        print("\n❌ Could not download/fix ChromeDriver")
        print("💡 Recommendation: Use the application's Sample Data mode")

if __name__ == "__main__":
    main()
