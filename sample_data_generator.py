import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

def generate_sample_fps_data(num_records=100, data_type='sales'):
    """
    Generate sample FPS data for testing the application
    """
    
    # Common commodities in Maharashtra FPS
    commodities = [
        "Rice", "Wheat", "Sugar", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
        "Chana Dal", "Ra<PERSON>", "<PERSON><PERSON>", "Black Gram", "Coarse Grain"
    ]
    
    # Sample FPS IDs
    fps_ids = [f"FPS{str(i).zfill(4)}" for i in range(1001, 1021)]
    
    # Sample beneficiary IDs
    beneficiary_ids = [f"BEN{str(i).zfill(8)}" for i in range(10000001, 10000001 + num_records)]
    
    if data_type == 'sales':
        data = []
        
        for i in range(num_records):
            record = {
                'type': 'sales',
                'fps_id': random.choice(fps_ids),
                'beneficiary_id': random.choice(beneficiary_ids),
                'commodity': random.choice(commodities),
                'quantity': round(random.uniform(1, 50), 2),
                'amount': round(random.uniform(10, 500), 2),
                'date': (datetime.now() - timedelta(days=random.randint(1, 30))).strftime('%Y-%m-%d'),
                'scraped_at': datetime.now().isoformat()
            }
            data.append(record)
        
        return pd.DataFrame(data)
    
    elif data_type == 'stock':
        data = []
        
        for fps_id in fps_ids:
            for commodity in commodities:
                opening_stock = random.uniform(50, 500)
                received = random.uniform(0, 200)
                issued = random.uniform(0, min(opening_stock + received, 300))
                closing_stock = opening_stock + received - issued
                
                record = {
                    'type': 'stock',
                    'fps_id': fps_id,
                    'commodity': commodity,
                    'opening_stock': round(opening_stock, 2),
                    'received': round(received, 2),
                    'issued': round(issued, 2),
                    'closing_stock': round(closing_stock, 2),
                    'scraped_at': datetime.now().isoformat()
                }
                data.append(record)
        
        return pd.DataFrame(data)
    
    else:
        raise ValueError("data_type must be 'sales' or 'stock'")

def generate_previous_month_data(current_data, missing_percentage=0.1):
    """
    Generate previous month data with some missing transactions
    """
    # Create a copy of current data
    previous_data = current_data.copy()
    
    # Modify dates to previous month
    previous_data['date'] = pd.to_datetime(previous_data['date']) - timedelta(days=30)
    previous_data['date'] = previous_data['date'].dt.strftime('%Y-%m-%d')
    
    # Randomly modify some quantities and amounts
    previous_data['quantity'] = previous_data['quantity'] * np.random.uniform(0.8, 1.2, len(previous_data))
    previous_data['amount'] = previous_data['amount'] * np.random.uniform(0.8, 1.2, len(previous_data))
    
    # Add some additional transactions that won't be in current month
    additional_records = int(len(previous_data) * missing_percentage)
    
    commodities = previous_data['commodity'].unique()
    fps_ids = previous_data['fps_id'].unique()
    
    for i in range(additional_records):
        record = {
            'type': 'sales',
            'fps_id': random.choice(fps_ids),
            'beneficiary_id': f"BEN{str(random.randint(20000001, 20000100)).zfill(8)}",
            'commodity': random.choice(commodities),
            'quantity': round(random.uniform(1, 50), 2),
            'amount': round(random.uniform(10, 500), 2),
            'date': (datetime.now() - timedelta(days=random.randint(31, 60))).strftime('%Y-%m-%d'),
            'scraped_at': datetime.now().isoformat()
        }
        previous_data = pd.concat([previous_data, pd.DataFrame([record])], ignore_index=True)
    
    return previous_data

def save_sample_data():
    """
    Generate and save sample data files
    """
    # Generate current month sales data
    current_sales = generate_sample_fps_data(150, 'sales')
    current_sales.to_csv('sample_current_month_sales.csv', index=False)
    
    # Generate stock data
    stock_data = generate_sample_fps_data(data_type='stock')
    stock_data.to_csv('sample_stock_data.csv', index=False)
    
    # Generate previous month data with some missing transactions
    previous_sales = generate_previous_month_data(current_sales, 0.15)
    previous_sales.to_csv('sample_previous_month_sales.csv', index=False)
    
    print("Sample data files generated:")
    print("- sample_current_month_sales.csv")
    print("- sample_previous_month_sales.csv")
    print("- sample_stock_data.csv")
    
    return current_sales, previous_sales, stock_data

if __name__ == "__main__":
    save_sample_data()
