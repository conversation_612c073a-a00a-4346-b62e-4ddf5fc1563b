#!/usr/bin/env python3
"""
Test script for Maharashtra FPS Data Analyzer
"""

import pandas as pd
import sys
import os

def test_sample_data():
    """Test if sample data files exist and are readable"""
    print("🧪 Testing Sample Data Files...")
    
    files_to_test = [
        'sample_current_month_sales.csv',
        'sample_previous_month_sales.csv',
        'sample_stock_data.csv'
    ]
    
    for file_name in files_to_test:
        try:
            if os.path.exists(file_name):
                df = pd.read_csv(file_name)
                print(f"✅ {file_name}: {len(df)} records loaded successfully")
                print(f"   Columns: {list(df.columns)}")
            else:
                print(f"❌ {file_name}: File not found")
        except Exception as e:
            print(f"❌ {file_name}: Error reading file - {str(e)}")
    
    print()

def test_data_generator():
    """Test the sample data generator"""
    print("🧪 Testing Sample Data Generator...")
    
    try:
        from sample_data_generator import generate_sample_fps_data, save_sample_data
        
        # Test sales data generation
        sales_data = generate_sample_fps_data(10, 'sales')
        print(f"✅ Sales data generation: {len(sales_data)} records")
        
        # Test stock data generation
        stock_data = generate_sample_fps_data(data_type='stock')
        print(f"✅ Stock data generation: {len(stock_data)} records")
        
        print("✅ Sample data generator working correctly")
        
    except Exception as e:
        print(f"❌ Sample data generator error: {str(e)}")
    
    print()

def test_app_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing Application Imports...")
    
    required_modules = [
        'streamlit',
        'pandas',
        'numpy',
        'plotly.express',
        'plotly.graph_objects',
        'requests',
        'beautifulsoup4',
        'selenium'
    ]
    
    for module in required_modules:
        try:
            if module == 'beautifulsoup4':
                import bs4
                print(f"✅ {module} (bs4): Available")
            elif module == 'plotly.express':
                import plotly.express as px
                print(f"✅ {module}: Available")
            elif module == 'plotly.graph_objects':
                import plotly.graph_objects as go
                print(f"✅ {module}: Available")
            else:
                __import__(module)
                print(f"✅ {module}: Available")
        except ImportError as e:
            print(f"❌ {module}: Not available - {str(e)}")
    
    print()

def test_app_functions():
    """Test key application functions"""
    print("🧪 Testing Application Functions...")
    
    try:
        # Import app functions
        sys.path.append('.')
        from app import scrape_with_requests, find_missing_transactions
        
        # Test fallback scraping
        print("Testing fallback scraping method...")
        test_data = scrape_with_requests("June", 2025, "Pune", "", True, False, False, 1)
        if test_data is not None:
            print(f"✅ Fallback scraping: {len(test_data)} records generated")
        else:
            print("❌ Fallback scraping: No data returned")
        
        # Test missing transaction detection
        if test_data is not None and len(test_data) > 5:
            current_data = test_data.head(3)
            previous_data = test_data.tail(5)
            missing = find_missing_transactions(current_data, previous_data)
            print(f"✅ Missing transaction detection: {len(missing)} missing transactions found")
        
    except Exception as e:
        print(f"❌ Application functions error: {str(e)}")
    
    print()

def regenerate_sample_data():
    """Regenerate sample data if needed"""
    print("🔄 Regenerating Sample Data...")
    
    try:
        from sample_data_generator import save_sample_data
        save_sample_data()
        print("✅ Sample data regenerated successfully")
    except Exception as e:
        print(f"❌ Error regenerating sample data: {str(e)}")
    
    print()

def main():
    print("🏪 Maharashtra FPS Data Analyzer - Test Suite")
    print("=" * 50)
    
    # Run tests
    test_app_imports()
    test_sample_data()
    test_data_generator()
    test_app_functions()
    
    # Check if sample data needs regeneration
    if not os.path.exists('sample_current_month_sales.csv'):
        regenerate_sample_data()
        test_sample_data()
    
    print("🎉 Test suite completed!")
    print("\n💡 To start the application, run:")
    print("   streamlit run app.py")
    print("\n📊 The application includes:")
    print("   • Sample data for testing")
    print("   • Fallback mode when Selenium fails")
    print("   • Interactive visualizations")
    print("   • Transaction analysis tools")

if __name__ == "__main__":
    main()
