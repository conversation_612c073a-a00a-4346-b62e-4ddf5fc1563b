# 🚀 Getting Started - Maharashtra FPS Data Analyzer

## Quick Start Guide

### 1. **Launch the Application**
```bash
streamlit run app.py
```
The application will open in your browser at `http://localhost:8501`

### 2. **First Time Setup**

#### Option A: Use Demo Data (Recommended for first-time users)
1. Go to **🔍 Data Scraping** section
2. Click **📊 Load Demo Data** button
3. This loads pre-generated sample data for testing

#### Option B: Generate Sample Data
1. Go to **🔍 Data Scraping** section
2. Select **📊 Sample Data (For testing/demo)** option
3. Click **🚀 Start Data Collection**
4. This generates new sample data based on your parameters

#### Option C: Real Website Scraping (Advanced)
1. Ensure Google Chrome is installed
2. Go to **🔍 Data Scraping** section
3. Select **🤖 Selenium (Real website scraping)** option
4. Choose your parameters (Month, Year, District)
5. Click **🚀 Start Data Collection**

### 3. **Upload Previous Month Data**
1. In the **🔍 Data Scraping** section
2. Use the file uploader to upload CSV/Excel files
3. Or use the provided `sample_previous_month_sales.csv`

### 4. **Explore Features**

#### 📊 Transaction Analysis
- View current vs previous month statistics
- Identify missing transactions
- Download missing transaction reports

#### 📦 Stock Management
- Input your current shop stock
- Compare with government records
- Get low stock alerts

#### 📈 Visualizations
- Interactive charts and graphs
- Performance metrics dashboard
- Month-over-month comparisons

## 🎯 Key Features Walkthrough

### **Missing Transaction Detection**
1. Load current month data (via scraping or demo)
2. Upload previous month data
3. Go to **📊 Transaction Analysis**
4. View missing transactions that were done last month but not this month

### **Stock Management**
1. Go to **📦 Stock Management**
2. Enter quantities for each commodity
3. Save stock data
4. View stock level alerts and comparisons

### **Data Visualization**
1. Go to **📈 Visualizations**
2. Choose from different chart types:
   - Transaction Trends
   - Stock Analysis
   - Comparison Charts
   - Performance Metrics

## 🛠️ Troubleshooting

### **Selenium Scraping Issues**
- **Error**: "WebDriver initialization failed"
- **Solution**: Use "Sample Data" mode instead
- **Alternative**: Click "Load Demo Data" button

### **No Data Available**
- **Issue**: Empty charts or tables
- **Solution**: Load demo data first from Data Scraping section

### **File Upload Issues**
- **Issue**: CSV upload fails
- **Solution**: Ensure file has proper headers (type, fps_id, beneficiary_id, commodity, quantity, amount, date)

### **Performance Issues**
- **Issue**: Application runs slowly
- **Solution**: Use smaller datasets or restart the application

## 📁 Sample Data Files

The application includes pre-generated sample files:
- `sample_current_month_sales.csv` - Current month transactions
- `sample_previous_month_sales.csv` - Previous month transactions  
- `sample_stock_data.csv` - Stock information

## 🔧 Advanced Usage

### **Custom Data Format**
Your CSV files should have these columns:
```
type,fps_id,beneficiary_id,commodity,quantity,amount,date,scraped_at
sales,FPS1001,BEN10000001,Rice,25.5,127.50,2025-06-15,2025-06-15T10:30:00
```

### **Selenium Configuration**
For real website scraping:
- Ensure stable internet connection
- Government website must be accessible
- Chrome browser required
- Allow extra time for data collection

## 📞 Support

### **Common Solutions**
1. **Start with demo data** to test all features
2. **Use sample data mode** if Selenium fails
3. **Check file formats** when uploading data
4. **Restart application** if issues persist

### **Feature Testing Order**
1. Load demo data
2. Test transaction analysis
3. Input stock data
4. Explore visualizations
5. Try real scraping (optional)

## 🎉 Success Indicators

You'll know everything is working when you see:
- ✅ Data loaded successfully messages
- 📊 Charts and graphs displaying
- 📋 Data tables with sample information
- 🚨 Stock alerts and missing transaction reports

---

**Ready to start?** Run `streamlit run app.py` and begin with the demo data!
