# Maharashtra FPS Data Analyzer

A comprehensive Streamlit application for analyzing Fair Price Shop (FPS) data from the Maharashtra AePDS (Aadhaar enabled Public Distribution System) website.

## Features

### 🔍 Data Scraping
- **Web Scraping**: Extract data from Maharashtra AePDS website
- **Sales Register**: Scrape FPS transaction data
- **Stock Details**: Extract stock information
- **Automated Data Collection**: Selenium-based scraping for dynamic content

### 📊 Transaction Analysis
- **Current Month Analysis**: Analyze current month transactions
- **Previous Month Comparison**: Upload and compare with previous month data
- **Missing Transaction Detection**: Identify transactions done last month but not this month
- **Beneficiary Analysis**: Track unique beneficiaries and their transactions

### 📦 Stock Management
- **Current Stock Input**: Manual input of current shop stock
- **Stock Comparison**: Compare with government records
- **Stock Alerts**: Low stock threshold warnings
- **Inventory Tracking**: Track stock levels for all commodities

### 📈 Visualizations
- **Transaction Trends**: Bar charts, pie charts, and time series
- **Stock Analysis**: Stock level indicators and distribution charts
- **Comparison Charts**: Month-over-month comparisons
- **Performance Metrics**: KPI dashboard with key metrics

## Installation

1. **Clone or download the project files**
2. **Install required dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Install Chrome WebDriver** (for web scraping):
   - The application uses `webdriver-manager` to automatically download ChromeDriver
   - Ensure you have Google Chrome installed

## Usage

### 1. Start the Application
```bash
streamlit run app.py
```

### 2. Navigate Through Sections

#### 🏠 Dashboard
- Overview of all features
- Quick statistics if data is loaded
- Status of loaded data sources

#### 🔍 Data Scraping
- **Select Parameters**:
  - Month and Year
  - District (from Maharashtra districts list)
  - FPS ID (optional, leave empty for all FPS in district)
- **Choose Data Types**:
  - Sales Register Data
  - Stock Details
  - Transaction Details
- **Scraping Options**:
  - Use Selenium for dynamic content
  - Delay between requests
- **Upload Previous Month Data**:
  - Upload CSV/Excel files with last month's transactions

#### 📊 Transaction Analysis
- View current and previous month statistics
- Identify missing transactions
- Download missing transaction reports
- Visualize transaction patterns

#### 📦 Stock Management
- **Input Current Stock**:
  - Enter quantities for common commodities
  - Add custom commodities
- **Stock Analysis**:
  - Compare with government records
  - Set low stock thresholds
  - Get stock alerts

#### 📈 Visualizations
- **Transaction Trends**: Volume and amount analysis
- **Stock Analysis**: Current levels and distribution
- **Comparison Charts**: Month-over-month comparisons
- **Performance Metrics**: KPI dashboard

## Sample Data

For testing purposes, you can generate sample data:

```bash
python sample_data_generator.py
```

This will create:
- `sample_current_month_sales.csv`
- `sample_previous_month_sales.csv`
- `sample_stock_data.csv`

## Data Format

### Sales Data Format
```csv
type,fps_id,beneficiary_id,commodity,quantity,amount,date,scraped_at
sales,FPS1001,BEN10000001,Rice,25.5,127.50,2025-06-15,2025-06-15T10:30:00
```

### Stock Data Format
```csv
type,fps_id,commodity,opening_stock,received,issued,closing_stock,scraped_at
stock,FPS1001,Rice,100.0,50.0,75.0,75.0,2025-06-15T10:30:00
```

## Key Features Explained

### Missing Transaction Detection
The application compares current month transactions with previous month data to identify:
- Beneficiaries who received commodities last month but not this month
- Specific commodity transactions that are missing
- Patterns in missing transactions

### Stock Management
- **Manual Input**: Enter your current shop stock
- **Government Comparison**: Compare with scraped government data
- **Alerts**: Get warnings for low stock items
- **Discrepancy Detection**: Identify differences between your records and government data

### Web Scraping
- **Selenium Integration**: Handles dynamic content on the Maharashtra AePDS website
- **Form Automation**: Automatically fills and submits forms
- **Data Parsing**: Extracts structured data from HTML tables
- **Error Handling**: Robust error handling for network issues

## Troubleshooting

### Common Issues

1. **Selenium WebDriver Issues**:
   - Ensure Google Chrome is installed
   - Check internet connectivity
   - Try increasing delay between requests

2. **Data Scraping Failures**:
   - Website might be down or changed structure
   - Check if the selected district/FPS exists
   - Verify month/year parameters

3. **File Upload Issues**:
   - Ensure CSV/Excel files have proper headers
   - Check file encoding (UTF-8 recommended)
   - Verify data format matches expected structure

### Performance Tips

1. **Large Data Sets**:
   - Use smaller date ranges for scraping
   - Process data in batches
   - Consider using the delay option for scraping

2. **Memory Usage**:
   - Clear session state if needed
   - Restart the application for fresh start

## Government Website Information

This application scrapes data from:
- **Website**: https://mahaepos.gov.in/
- **Sales Register**: FPS_Trans_Abstract.jsp
- **Stock Details**: Stock_Register_Int.jsp

**Note**: Always ensure compliance with the website's terms of service and use reasonable delays between requests.

## Support

For issues or questions:
1. Check the troubleshooting section
2. Verify your data format
3. Test with sample data first
4. Check console logs for detailed error messages

## Disclaimer

This application is designed for educational and administrative purposes. Users are responsible for:
- Complying with website terms of service
- Ensuring data privacy and security
- Verifying scraped data accuracy
- Following government regulations for PDS data handling
